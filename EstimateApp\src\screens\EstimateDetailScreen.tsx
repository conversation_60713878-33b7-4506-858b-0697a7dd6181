import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp as NavigationRouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useTheme } from '../contexts/ThemeContext';
import { Estimate } from '../types';
import { estimateService } from '../services/dataService';
import { ThemedCard } from '../components/ThemedCard';
import { ThemedButton } from '../components/ThemedButton';
import { EstimatesStackParamList } from '../navigation/EstimatesNavigator';

type NavigationProp = StackNavigationProp<EstimatesStackParamList, 'EstimateDetail'>;
type EstimateDetailRouteProp = NavigationRouteProp<EstimatesStackParamList, 'EstimateDetail'>;

export const EstimateDetailScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<EstimateDetailRouteProp>();
  const { estimateId } = route.params;

  const [estimate, setEstimate] = useState<Estimate | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
      textAlign: 'center',
    },
    editButton: {
      padding: 8,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 20,
    },
    estimateHeader: {
      marginBottom: 24,
    },
    estimateTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
    },
    estimateDescription: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      marginBottom: 12,
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    statusBadge: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    statusText: {
      fontSize: 14,
      fontWeight: '600',
    },
    totalAmount: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    infoLabel: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      flex: 1,
    },
    infoValue: {
      fontSize: 14,
      color: theme.colors.text,
      fontWeight: '500',
    },
    itemRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    itemDescription: {
      fontSize: 14,
      color: theme.colors.text,
      flex: 1,
    },
    itemQuantity: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      width: 60,
      textAlign: 'center',
    },
    itemPrice: {
      fontSize: 14,
      color: theme.colors.text,
      fontWeight: '500',
      width: 80,
      textAlign: 'right',
    },
    actionButtons: {
      flexDirection: 'row',
      gap: 12,
      marginTop: 20,
    },
    actionButton: {
      flex: 1,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: 16,
      color: theme.colors.text,
    },
  });

  useEffect(() => {
    loadEstimate();
  }, [estimateId]);

  const loadEstimate = async () => {
    try {
      setIsLoading(true);
      const estimateData = await estimateService.getById(estimateId);
      if (estimateData) {
        setEstimate(estimateData);
      } else {
        Alert.alert('Error', 'Estimate not found');
        navigation.goBack();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load estimate');
      navigation.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: Estimate['status']) => {
    switch (status) {
      case 'Draft':
        return { backgroundColor: theme.colors.textSecondary + '20', color: theme.colors.textSecondary };
      case 'Pending':
        return { backgroundColor: theme.colors.warning + '20', color: theme.colors.warning };
      case 'Approved':
        return { backgroundColor: theme.colors.success + '20', color: theme.colors.success };
      case 'Rejected':
        return { backgroundColor: theme.colors.error + '20', color: theme.colors.error };
      case 'Expired':
        return { backgroundColor: theme.colors.textSecondary + '20', color: theme.colors.textSecondary };
      default:
        return { backgroundColor: theme.colors.textSecondary + '20', color: theme.colors.textSecondary };
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleExportPDF = () => {
    Alert.alert('Export PDF', 'PDF export functionality will be implemented here');
  };

  const handleSendEmail = () => {
    Alert.alert('Send Email', 'Email functionality will be implemented here');
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading estimate...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!estimate) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Estimate not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  const statusStyle = getStatusColor(estimate.status);
  const clientName = estimate.client
    ? `${estimate.client.firstName} ${estimate.client.lastName}`
    : 'Unknown Client';

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Estimate Details</Text>
        <TouchableOpacity
          style={styles.editButton}
          onPress={() => navigation.navigate('EstimateBuilder', { estimateId })}
        >
          <Ionicons name="create-outline" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.estimateHeader}>
          <Text style={styles.estimateTitle}>{estimate.title}</Text>
          <Text style={styles.estimateDescription}>{estimate.description}</Text>
          <View style={styles.statusContainer}>
            <View style={[styles.statusBadge, { backgroundColor: statusStyle.backgroundColor }]}>
              <Text style={[styles.statusText, { color: statusStyle.color }]}>
                {estimate.status}
              </Text>
            </View>
            <Text style={styles.totalAmount}>
              {formatCurrency(estimate.total)}
            </Text>
          </View>
        </View>

        <ThemedCard>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Client Information</Text>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Client</Text>
              <Text style={styles.infoValue}>{clientName}</Text>
            </View>
            {estimate.client?.company && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Company</Text>
                <Text style={styles.infoValue}>{estimate.client.company}</Text>
              </View>
            )}
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Created</Text>
              <Text style={styles.infoValue}>{formatDate(estimate.createdAt)}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Valid Until</Text>
              <Text style={styles.infoValue}>{formatDate(estimate.validUntil)}</Text>
            </View>
          </View>
        </ThemedCard>

        <ThemedCard>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Items</Text>
            {estimate.items.map((item, index) => (
              <View key={item.id} style={styles.itemRow}>
                <Text style={styles.itemDescription}>{item.description}</Text>
                <Text style={styles.itemQuantity}>{item.quantity} {item.unit}</Text>
                <Text style={styles.itemPrice}>{formatCurrency(item.total)}</Text>
              </View>
            ))}
          </View>
        </ThemedCard>

        <ThemedCard>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Summary</Text>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Subtotal</Text>
              <Text style={styles.infoValue}>{formatCurrency(estimate.subtotal)}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Tax</Text>
              <Text style={styles.infoValue}>{formatCurrency(estimate.tax)}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Total</Text>
              <Text style={[styles.infoValue, { fontSize: 18, fontWeight: 'bold', color: theme.colors.primary }]}>
                {formatCurrency(estimate.total)}
              </Text>
            </View>
          </View>
        </ThemedCard>

        <View style={styles.actionButtons}>
          <ThemedButton
            title="Export PDF"
            onPress={handleExportPDF}
            variant="outline"
            style={styles.actionButton}
          />
          <ThemedButton
            title="Send Email"
            onPress={handleSendEmail}
            variant="primary"
            style={styles.actionButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
