import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { ThemedCard } from '../components/ThemedCard';
import { ThemedButton } from '../components/ThemedButton';

export const ProfileScreen: React.FC = () => {
  const { theme, toggleTheme } = useTheme();
  const { authState, logout } = useAuth();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 20,
    },
    profileHeader: {
      alignItems: 'center',
      marginBottom: 24,
    },
    avatar: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 12,
    },
    avatarText: {
      fontSize: 28,
      fontWeight: 'bold',
      color: '#FFFFFF',
    },
    userName: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    userEmail: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 8,
    },
    roleBadge: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      backgroundColor: theme.colors.primary + '20',
    },
    roleText: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.primary,
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    menuItemLast: {
      borderBottomWidth: 0,
    },
    menuIcon: {
      marginRight: 12,
      width: 24,
    },
    menuText: {
      fontSize: 16,
      color: theme.colors.text,
      flex: 1,
    },
    menuValue: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginRight: 8,
    },
    menuArrow: {
      marginLeft: 8,
    },
    logoutButton: {
      marginTop: 20,
    },
    version: {
      textAlign: 'center',
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginTop: 20,
    },
  });

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: logout,
        },
      ]
    );
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const menuItems = [
    {
      icon: 'person-outline' as keyof typeof Ionicons.glyphMap,
      title: 'Edit Profile',
      onPress: () => Alert.alert('Info', 'Edit profile functionality'),
    },
    {
      icon: 'notifications-outline' as keyof typeof Ionicons.glyphMap,
      title: 'Notifications',
      value: 'Enabled',
      onPress: () => Alert.alert('Info', 'Notification settings'),
    },
    {
      icon: 'shield-outline' as keyof typeof Ionicons.glyphMap,
      title: 'Privacy & Security',
      onPress: () => Alert.alert('Info', 'Privacy settings'),
    },
    {
      icon: 'document-text-outline' as keyof typeof Ionicons.glyphMap,
      title: 'Export Data',
      onPress: () => Alert.alert('Info', 'Export data functionality'),
    },
    {
      icon: 'help-circle-outline' as keyof typeof Ionicons.glyphMap,
      title: 'Help & Support',
      onPress: () => Alert.alert('Info', 'Help and support'),
    },
    {
      icon: 'information-circle-outline' as keyof typeof Ionicons.glyphMap,
      title: 'About',
      onPress: () => Alert.alert('About', 'Construction Estimate Manager v1.0.0'),
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Profile</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.profileHeader}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {authState.user && getInitials(authState.user.firstName, authState.user.lastName)}
            </Text>
          </View>
          <Text style={styles.userName}>
            {authState.user?.firstName} {authState.user?.lastName}
          </Text>
          <Text style={styles.userEmail}>{authState.user?.email}</Text>
          <View style={styles.roleBadge}>
            <Text style={styles.roleText}>{authState.user?.role}</Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          <ThemedCard padding={0}>
            <TouchableOpacity style={styles.menuItem} onPress={toggleTheme}>
              <Ionicons
                name={theme.isDark ? 'sunny' : 'moon'}
                size={24}
                color={theme.colors.primary}
                style={styles.menuIcon}
              />
              <Text style={styles.menuText}>Theme</Text>
              <Text style={styles.menuValue}>
                {theme.isDark ? 'Dark' : 'Light'}
              </Text>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={theme.colors.textSecondary}
                style={styles.menuArrow}
              />
            </TouchableOpacity>
          </ThemedCard>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          <ThemedCard padding={0}>
            {menuItems.map((item, index) => (
              <TouchableOpacity
                key={item.title}
                style={[
                  styles.menuItem,
                  index === menuItems.length - 1 && styles.menuItemLast,
                ]}
                onPress={item.onPress}
              >
                <Ionicons
                  name={item.icon}
                  size={24}
                  color={theme.colors.primary}
                  style={styles.menuIcon}
                />
                <Text style={styles.menuText}>{item.title}</Text>
                {item.value && (
                  <Text style={styles.menuValue}>{item.value}</Text>
                )}
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={theme.colors.textSecondary}
                  style={styles.menuArrow}
                />
              </TouchableOpacity>
            ))}
          </ThemedCard>
        </View>

        <ThemedButton
          title="Sign Out"
          onPress={handleLogout}
          variant="danger"
          size="large"
          style={styles.logoutButton}
        />

        <Text style={styles.version}>
          Construction Estimate Manager v1.0.0
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
};
