// WebSocket polyfill for React Native
// This provides a compatible interface for the ws module used by Supabase

// Use React Native's built-in WebSocket
const NativeWebSocket = global.WebSocket;

// Create a WebSocket class that mimics the ws module interface
class WebSocketPolyfill extends NativeWebSocket {
  constructor(url, protocols, options = {}) {
    // Extract headers from options if provided
    const { headers, ...otherOptions } = options;
    
    // React Native WebSocket doesn't support headers in constructor
    // but we can store them for potential use
    super(url, protocols);
    
    this._headers = headers;
    this._options = otherOptions;
    
    // Add compatibility methods
    this.ping = this.ping || (() => {});
    this.pong = this.pong || (() => {});
    this.terminate = this.terminate || this.close.bind(this);
  }
  
  // Add compatibility for ws module events
  ping(data, mask, callback) {
    if (typeof callback === 'function') {
      callback();
    }
  }
  
  pong(data, mask, callback) {
    if (typeof callback === 'function') {
      callback();
    }
  }
  
  terminate() {
    this.close();
  }
}

// WebSocket Server mock (not supported in React Native)
class WebSocketServer {
  constructor(options = {}) {
    console.warn('WebSocketServer is not supported in React Native environment');
    this.clients = new Set();
    this._options = options;
  }
  
  on(event, callback) {
    // Mock event handling
    return this;
  }
  
  close(callback) {
    if (typeof callback === 'function') {
      callback();
    }
  }
  
  handleUpgrade() {
    // Mock method
  }
}

// Export the polyfill
module.exports = WebSocketPolyfill;
module.exports.WebSocket = WebSocketPolyfill;
module.exports.WebSocketServer = WebSocketServer;
module.exports.Server = WebSocketServer;
module.exports.default = WebSocketPolyfill;

// Additional exports that might be expected
module.exports.createWebSocketStream = () => {
  throw new Error('createWebSocketStream is not supported in React Native');
};

module.exports.CONNECTING = NativeWebSocket.CONNECTING;
module.exports.OPEN = NativeWebSocket.OPEN;
module.exports.CLOSING = NativeWebSocket.CLOSING;
module.exports.CLOSED = NativeWebSocket.CLOSED;
