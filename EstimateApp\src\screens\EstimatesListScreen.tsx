import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useTheme } from '../contexts/ThemeContext';
import { Estimate } from '../types';
import { estimateService } from '../services/dataService';
import { ThemedCard } from '../components/ThemedCard';
import { ThemedButton } from '../components/ThemedButton';
import { SearchBar } from '../components/SearchBar';
import { EstimatesStackParamList } from '../navigation/EstimatesNavigator';

type NavigationProp = StackNavigationProp<EstimatesStackParamList, 'EstimatesList'>;

export const EstimatesListScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const [estimates, setEstimates] = useState<Estimate[]>([]);
  const [filteredEstimates, setFilteredEstimates] = useState<Estimate[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    addButton: {
      padding: 8,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 16,
    },
    estimateCard: {
      marginBottom: 12,
    },
    estimateHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    estimateTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
    },
    statusBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      alignSelf: 'flex-start',
    },
    statusText: {
      fontSize: 12,
      fontWeight: '600',
    },
    estimateClient: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 8,
    },
    estimateDetails: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    estimateAmount: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    estimateDate: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    },
    emptyIcon: {
      marginBottom: 16,
    },
    emptyTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    emptySubtitle: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: 24,
    },
  });

  useEffect(() => {
    loadEstimates();
  }, []);

  useEffect(() => {
    filterEstimates();
  }, [estimates, searchQuery]);

  const loadEstimates = async () => {
    try {
      setIsLoading(true);
      const estimatesData = await estimateService.getAll();
      setEstimates(estimatesData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load estimates');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadEstimates();
    setIsRefreshing(false);
  };

  const filterEstimates = () => {
    if (!searchQuery.trim()) {
      setFilteredEstimates(estimates);
    } else {
      const filtered = estimates.filter(estimate =>
        estimate.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        estimate.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        estimate.client?.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        estimate.client?.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        estimate.client?.company?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredEstimates(filtered);
    }
  };

  const getStatusColor = (status: Estimate['status']) => {
    switch (status) {
      case 'Draft':
        return { backgroundColor: theme.colors.textSecondary + '20', color: theme.colors.textSecondary };
      case 'Pending':
        return { backgroundColor: theme.colors.warning + '20', color: theme.colors.warning };
      case 'Approved':
        return { backgroundColor: theme.colors.success + '20', color: theme.colors.success };
      case 'Rejected':
        return { backgroundColor: theme.colors.error + '20', color: theme.colors.error };
      case 'Expired':
        return { backgroundColor: theme.colors.textSecondary + '20', color: theme.colors.textSecondary };
      default:
        return { backgroundColor: theme.colors.textSecondary + '20', color: theme.colors.textSecondary };
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const renderEstimateCard = ({ item }: { item: Estimate }) => {
    const statusStyle = getStatusColor(item.status);
    const clientName = item.client
      ? `${item.client.firstName} ${item.client.lastName}`
      : 'Unknown Client';

    return (
      <ThemedCard
        style={styles.estimateCard}
        onPress={() => navigation.navigate('EstimateDetail', { estimateId: item.id })}
      >
        <View style={styles.estimateHeader}>
          <Text style={styles.estimateTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <View style={[styles.statusBadge, { backgroundColor: statusStyle.backgroundColor }]}>
            <Text style={[styles.statusText, { color: statusStyle.color }]}>
              {item.status}
            </Text>
          </View>
        </View>

        <Text style={styles.estimateClient}>
          {clientName}
          {item.client?.company && ` • ${item.client.company}`}
        </Text>

        <View style={styles.estimateDetails}>
          <Text style={styles.estimateAmount}>
            {formatCurrency(item.total)}
          </Text>
          <Text style={styles.estimateDate}>
            {formatDate(item.createdAt)}
          </Text>
        </View>
      </ThemedCard>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons
        name="document-text-outline"
        size={64}
        color={theme.colors.textSecondary}
        style={styles.emptyIcon}
      />
      <Text style={styles.emptyTitle}>No Estimates Found</Text>
      <Text style={styles.emptySubtitle}>
        {searchQuery
          ? 'No estimates match your search criteria. Try adjusting your search terms.'
          : 'Start creating estimates for your clients to track your projects and revenue.'}
      </Text>
      {!searchQuery && (
        <ThemedButton
          title="Create First Estimate"
          onPress={() => navigation.navigate('EstimateBuilder', {})}
          variant="primary"
        />
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Estimates</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('EstimateBuilder', {})}
        >
          <Ionicons name="add" size={28} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search estimates..."
        />

        <FlatList
          data={filteredEstimates}
          renderItem={renderEstimateCard}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor={theme.colors.primary}
            />
          }
          ListEmptyComponent={!isLoading ? renderEmptyState : null}
        />
      </View>
    </SafeAreaView>
  );
};
