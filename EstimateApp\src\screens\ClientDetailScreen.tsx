import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp as NavigationRouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useTheme } from '../contexts/ThemeContext';
import { Client } from '../types';
import { clientService } from '../services/dataService';
import { ThemedCard } from '../components/ThemedCard';
import { ThemedButton } from '../components/ThemedButton';
import { ClientsStackParamList } from '../navigation/ClientsNavigator';

type NavigationProp = StackNavigationProp<ClientsStackParamList, 'ClientDetail'>;
type ClientDetailRouteProp = NavigationRouteProp<ClientsStackParamList, 'ClientDetail'>;

export const ClientDetailScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<ClientDetailRouteProp>();
  const { clientId } = route.params;

  const [client, setClient] = useState<Client | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
      textAlign: 'center',
    },
    editButton: {
      padding: 8,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 20,
    },
    clientHeader: {
      alignItems: 'center',
      marginBottom: 24,
    },
    clientAvatar: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 12,
    },
    clientAvatarText: {
      fontSize: 28,
      fontWeight: 'bold',
      color: '#FFFFFF',
    },
    clientName: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    clientCompany: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      marginBottom: 8,
    },
    statusBadge: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    statusText: {
      fontSize: 14,
      fontWeight: '600',
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    infoIcon: {
      marginRight: 12,
      width: 20,
    },
    infoLabel: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      flex: 1,
    },
    infoValue: {
      fontSize: 14,
      color: theme.colors.text,
      fontWeight: '500',
    },
    actionButtons: {
      flexDirection: 'row',
      gap: 12,
      marginTop: 20,
    },
    actionButton: {
      flex: 1,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: 16,
      color: theme.colors.text,
    },
  });

  useEffect(() => {
    loadClient();
  }, [clientId]);

  const loadClient = async () => {
    try {
      setIsLoading(true);
      const clientData = await clientService.getById(clientId);
      if (clientData) {
        setClient(clientData);
      } else {
        Alert.alert('Error', 'Client not found');
        navigation.goBack();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load client');
      navigation.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Client',
      'Are you sure you want to delete this client? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await clientService.delete(clientId);
              Alert.alert('Success', 'Client deleted successfully');
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete client');
            }
          },
        },
      ]
    );
  };

  const getStatusColor = (status: Client['status']) => {
    switch (status) {
      case 'Active':
        return { backgroundColor: theme.colors.success + '20', color: theme.colors.success };
      case 'Pending':
        return { backgroundColor: theme.colors.warning + '20', color: theme.colors.warning };
      case 'Inactive':
        return { backgroundColor: theme.colors.textSecondary + '20', color: theme.colors.textSecondary };
      default:
        return { backgroundColor: theme.colors.textSecondary + '20', color: theme.colors.textSecondary };
    }
  };

  const getClientInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading client...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!client) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Client not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  const statusStyle = getStatusColor(client.status);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Client Details</Text>
        <TouchableOpacity
          style={styles.editButton}
          onPress={() => navigation.navigate('AddEditClient', { clientId })}
        >
          <Ionicons name="create-outline" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.clientHeader}>
          <View style={styles.clientAvatar}>
            <Text style={styles.clientAvatarText}>
              {getClientInitials(client.firstName, client.lastName)}
            </Text>
          </View>
          <Text style={styles.clientName}>
            {client.firstName} {client.lastName}
          </Text>
          {client.company && (
            <Text style={styles.clientCompany}>{client.company}</Text>
          )}
          <View style={[styles.statusBadge, { backgroundColor: statusStyle.backgroundColor }]}>
            <Text style={[styles.statusText, { color: statusStyle.color }]}>
              {client.status}
            </Text>
          </View>
        </View>

        <ThemedCard>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contact Information</Text>

            <View style={styles.infoRow}>
              <Ionicons name="mail" size={20} color={theme.colors.primary} style={styles.infoIcon} />
              <Text style={styles.infoLabel}>Email</Text>
              <Text style={styles.infoValue}>{client.email}</Text>
            </View>

            <View style={styles.infoRow}>
              <Ionicons name="call" size={20} color={theme.colors.primary} style={styles.infoIcon} />
              <Text style={styles.infoLabel}>Phone</Text>
              <Text style={styles.infoValue}>{client.phone}</Text>
            </View>

            {client.company && (
              <View style={styles.infoRow}>
                <Ionicons name="business" size={20} color={theme.colors.primary} style={styles.infoIcon} />
                <Text style={styles.infoLabel}>Company</Text>
                <Text style={styles.infoValue}>{client.company}</Text>
              </View>
            )}
          </View>
        </ThemedCard>

        <ThemedCard>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Address</Text>

            <View style={styles.infoRow}>
              <Ionicons name="location" size={20} color={theme.colors.primary} style={styles.infoIcon} />
              <Text style={styles.infoLabel}>Street</Text>
              <Text style={styles.infoValue}>{client.address.street}</Text>
            </View>

            <View style={styles.infoRow}>
              <Ionicons name="location" size={20} color={theme.colors.primary} style={styles.infoIcon} />
              <Text style={styles.infoLabel}>City</Text>
              <Text style={styles.infoValue}>{client.address.city}</Text>
            </View>

            <View style={styles.infoRow}>
              <Ionicons name="location" size={20} color={theme.colors.primary} style={styles.infoIcon} />
              <Text style={styles.infoLabel}>State</Text>
              <Text style={styles.infoValue}>{client.address.state}</Text>
            </View>

            <View style={styles.infoRow}>
              <Ionicons name="location" size={20} color={theme.colors.primary} style={styles.infoIcon} />
              <Text style={styles.infoLabel}>ZIP Code</Text>
              <Text style={styles.infoValue}>{client.address.zipCode}</Text>
            </View>
          </View>
        </ThemedCard>

        <View style={styles.actionButtons}>
          <ThemedButton
            title="Create Estimate"
            onPress={() => {
              // Navigate to estimate builder with this client
              Alert.alert('Info', 'Navigate to estimate builder');
            }}
            variant="primary"
            style={styles.actionButton}
          />
          <ThemedButton
            title="Delete Client"
            onPress={handleDelete}
            variant="danger"
            style={styles.actionButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
