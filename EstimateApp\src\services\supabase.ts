import { createClient } from '@supabase/supabase-js';
import 'react-native-url-polyfill/auto';

// Replace these with your actual Supabase project URL and anon key
const supabaseUrl = 'https://your-project-ref.supabase.co';
const supabaseAnonKey = 'your-anon-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  realtime: {
    // Disable realtime to avoid WebSocket issues in React Native
    params: {
      eventsPerSecond: 0,
    },
  },
  global: {
    headers: {
      'X-Client-Info': 'react-native',
    },
  },
});

// Mock data for development - replace with actual Supabase calls
export const mockData = {
  users: [
    {
      id: '1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Admin',
      role: 'Admin' as const,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: '2',
      email: '<EMAIL>',
      firstName: 'Jane',
      lastName: 'Manager',
      role: 'Manager' as const,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ],
  clients: [
    {
      id: '1',
      firstName: 'Robert',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      company: 'Johnson Properties',
      status: 'Active' as const,
      address: {
        street: '123 Main St',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62701',
        country: 'USA',
      },
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z',
    },
    {
      id: '2',
      firstName: 'Sarah',
      lastName: 'Williams',
      email: '<EMAIL>',
      phone: '+****************',
      company: 'Williams Construction',
      status: 'Active' as const,
      address: {
        street: '456 Oak Ave',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62702',
        country: 'USA',
      },
      createdAt: '2024-01-20T00:00:00Z',
      updatedAt: '2024-01-20T00:00:00Z',
    },
    {
      id: '3',
      firstName: 'Michael',
      lastName: 'Davis',
      email: '<EMAIL>',
      phone: '+****************',
      company: 'Davis Enterprises',
      status: 'Active' as const,
      address: {
        street: '789 Pine St',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62703',
        country: 'USA',
      },
      createdAt: '2024-01-25T00:00:00Z',
      updatedAt: '2024-01-25T00:00:00Z',
    },
    {
      id: '4',
      firstName: 'Emily',
      lastName: 'Brown',
      email: '<EMAIL>',
      phone: '+****************',
      company: undefined,
      status: 'Pending' as const,
      address: {
        street: '321 Elm St',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62704',
        country: 'USA',
      },
      createdAt: '2024-02-01T00:00:00Z',
      updatedAt: '2024-02-01T00:00:00Z',
    },
  ],
  templates: [
    {
      id: '1',
      name: 'Basic Electrical Work',
      description: 'Standard electrical installation template',
      category: 'Electrical' as const,
      isPublic: true,
      createdBy: '1',
      items: [
        {
          id: '1',
          description: 'Electrical outlet installation',
          unit: 'each',
          estimatedPrice: 75,
          category: 'Installation',
        },
        {
          id: '2',
          description: 'Light fixture installation',
          unit: 'each',
          estimatedPrice: 125,
          category: 'Installation',
        },
      ],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: '2',
      name: 'Bathroom Renovation',
      description: 'Complete bathroom renovation package',
      category: 'Plumbing' as const,
      isPublic: true,
      createdBy: '1',
      items: [
        {
          id: '3',
          description: 'Toilet installation',
          unit: 'each',
          estimatedPrice: 350,
          category: 'Plumbing',
        },
        {
          id: '4',
          description: 'Sink and faucet installation',
          unit: 'each',
          estimatedPrice: 450,
          category: 'Plumbing',
        },
      ],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: '3',
      name: 'Kitchen Renovation',
      description: 'Complete kitchen renovation package',
      category: 'General Construction' as const,
      isPublic: true,
      createdBy: '1',
      items: [
        {
          id: '5',
          description: 'Cabinet installation',
          unit: 'linear foot',
          estimatedPrice: 150,
          category: 'Cabinetry',
        },
        {
          id: '6',
          description: 'Countertop installation',
          unit: 'sq ft',
          estimatedPrice: 85,
          category: 'Countertops',
        },
        {
          id: '7',
          description: 'Flooring installation',
          unit: 'sq ft',
          estimatedPrice: 12,
          category: 'Flooring',
        },
      ],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: '4',
      name: 'Roofing Repair',
      description: 'Standard roofing repair and maintenance',
      category: 'Roofing' as const,
      isPublic: true,
      createdBy: '1',
      items: [
        {
          id: '8',
          description: 'Shingle replacement',
          unit: 'sq ft',
          estimatedPrice: 8.50,
          category: 'Roofing',
        },
        {
          id: '9',
          description: 'Gutter cleaning and repair',
          unit: 'linear foot',
          estimatedPrice: 15,
          category: 'Gutters',
        },
      ],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ],
  estimates: [
    {
      id: '1',
      clientId: '1',
      title: 'Kitchen Electrical Upgrade',
      description: 'Upgrade kitchen electrical system with new outlets and lighting',
      status: 'Pending' as const,
      items: [
        {
          id: '1',
          description: 'GFCI outlet installation',
          quantity: 4,
          unit: 'each',
          unitPrice: 85,
          total: 340,
          category: 'Electrical',
        },
        {
          id: '2',
          description: 'Under-cabinet LED lighting',
          quantity: 1,
          unit: 'set',
          unitPrice: 450,
          total: 450,
          category: 'Lighting',
        },
      ],
      subtotal: 790,
      tax: 63.20,
      total: 853.20,
      validUntil: '2024-03-01T00:00:00Z',
      createdBy: '1',
      createdAt: '2024-02-01T00:00:00Z',
      updatedAt: '2024-02-01T00:00:00Z',
    },
    {
      id: '2',
      clientId: '2',
      title: 'Bathroom Renovation',
      description: 'Complete bathroom renovation with modern fixtures',
      status: 'Approved' as const,
      items: [
        {
          id: '3',
          description: 'Toilet installation',
          quantity: 1,
          unit: 'each',
          unitPrice: 350,
          total: 350,
          category: 'Plumbing',
        },
        {
          id: '4',
          description: 'Vanity and sink installation',
          quantity: 1,
          unit: 'each',
          unitPrice: 850,
          total: 850,
          category: 'Plumbing',
        },
        {
          id: '5',
          description: 'Tile flooring',
          quantity: 45,
          unit: 'sq ft',
          unitPrice: 18,
          total: 810,
          category: 'Flooring',
        },
      ],
      subtotal: 2010,
      tax: 160.80,
      total: 2170.80,
      validUntil: '2024-04-01T00:00:00Z',
      createdBy: '1',
      createdAt: '2024-02-05T00:00:00Z',
      updatedAt: '2024-02-05T00:00:00Z',
    },
    {
      id: '3',
      clientId: '3',
      title: 'Roof Repair',
      description: 'Emergency roof repair after storm damage',
      status: 'Draft' as const,
      items: [
        {
          id: '6',
          description: 'Shingle replacement',
          quantity: 250,
          unit: 'sq ft',
          unitPrice: 8.50,
          total: 2125,
          category: 'Roofing',
        },
        {
          id: '7',
          description: 'Gutter repair',
          quantity: 40,
          unit: 'linear foot',
          unitPrice: 15,
          total: 600,
          category: 'Gutters',
        },
      ],
      subtotal: 2725,
      tax: 218,
      total: 2943,
      validUntil: '2024-03-15T00:00:00Z',
      createdBy: '1',
      createdAt: '2024-02-10T00:00:00Z',
      updatedAt: '2024-02-10T00:00:00Z',
    },
  ],
};
