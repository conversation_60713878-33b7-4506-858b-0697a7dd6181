const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add resolver configuration to handle Node.js polyfills
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Configure Node.js polyfills for React Native
config.resolver.alias = {
  ...config.resolver.alias,
  // Polyfill Node.js modules that aren't available in React Native
  'stream': require.resolve('stream-browserify'),
  'events': require.resolve('events'),
  'buffer': require.resolve('buffer'),
  'process': require.resolve('process/browser'),
  'util': require.resolve('util'),
  'url': require.resolve('react-native-url-polyfill'),
  'querystring': require.resolve('querystring-es3'),
  'path': require.resolve('path-browserify'),
  // Disable Node.js-only modules that cause issues
  'fs': false,
  'net': false,
  'tls': false,
  'child_process': false,
  'os': false,
  'http': false,
  'https': false,
  'zlib': false,
  'crypto': require.resolve('react-native-crypto'),
  // Handle WebSocket library specifically - use our polyfill
  'ws': require.resolve('./ws-polyfill.js'),
  // Additional crypto modules that might cause issues
  'cipher-base': require.resolve('cipher-base'),
  'create-hash': require.resolve('create-hash'),
  'create-hmac': require.resolve('create-hmac'),
};

// Block problematic modules at the resolver level
config.resolver.blockList = [
  // Block specific problematic files that try to import Node.js modules
  /.*[\/\\]ws[\/\\]lib[\/\\]stream\.js$/,
  /.*[\/\\]ws[\/\\]lib[\/\\]websocket-server\.js$/,
  /.*[\/\\]ws[\/\\]lib[\/\\]sender\.js$/,
  /.*[\/\\]ws[\/\\]lib[\/\\]receiver\.js$/,
  /.*[\/\\]ws[\/\\]lib[\/\\]websocket\.js$/,
];

// Add global polyfills
config.transformer.getTransformOptions = async () => ({
  transform: {
    experimentalImportSupport: false,
    inlineRequires: true,
  },
});

module.exports = config;
