import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp as NavigationRouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useTheme } from '../contexts/ThemeContext';
import { EstimateForm, EstimateItem, Client } from '../types';
import { estimateService, clientService } from '../services/dataService';
import { ThemedCard } from '../components/ThemedCard';
import { ThemedButton } from '../components/ThemedButton';
import { ThemedTextInput } from '../components/ThemedTextInput';
import { ThemedModal } from '../components/ThemedModal';
import { EstimatesStackParamList } from '../navigation/EstimatesNavigator';

type NavigationProp = StackNavigationProp<EstimatesStackParamList, 'EstimateBuilder'>;
type EstimateBuilderRouteProp = NavigationRouteProp<EstimatesStackParamList, 'EstimateBuilder'>;

export const EstimateBuilderScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<EstimateBuilderRouteProp>();
  const { estimateId, templateId } = route.params;

  const [formData, setFormData] = useState<EstimateForm>({
    clientId: '',
    title: '',
    description: '',
    validUntil: '',
    items: [],
  });
  const [clients, setClients] = useState<Client[]>([]);
  const [showClientModal, setShowClientModal] = useState(false);
  const [showItemModal, setShowItemModal] = useState(false);
  const [editingItem, setEditingItem] = useState<EstimateItem | null>(null);
  const [newItem, setNewItem] = useState<Partial<EstimateItem>>({
    description: '',
    quantity: 1,
    unit: 'each',
    unitPrice: 0,
    category: '',
  });
  const [isLoading, setIsLoading] = useState(false);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
      textAlign: 'center',
    },
    saveButton: {
      padding: 8,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 20,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    clientSelector: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      backgroundColor: theme.colors.surface,
      marginBottom: 16,
    },
    clientText: {
      fontSize: 16,
      color: theme.colors.text,
      flex: 1,
    },
    clientPlaceholder: {
      color: theme.colors.textSecondary,
    },
    itemsHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 12,
    },
    addItemButton: {
      padding: 8,
    },
    itemCard: {
      marginBottom: 8,
    },
    itemRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    itemInfo: {
      flex: 1,
    },
    itemDescription: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 4,
    },
    itemDetails: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    itemPrice: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    itemActions: {
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: 12,
    },
    itemActionButton: {
      padding: 8,
      marginLeft: 4,
    },
    summary: {
      marginTop: 20,
    },
    summaryRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: 8,
    },
    summaryLabel: {
      fontSize: 16,
      color: theme.colors.text,
    },
    summaryValue: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    totalRow: {
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      paddingTop: 12,
      marginTop: 8,
    },
    totalValue: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    modalContent: {
      gap: 16,
    },
    modalButtons: {
      flexDirection: 'row',
      gap: 12,
      marginTop: 20,
    },
    modalButton: {
      flex: 1,
    },
    clientItem: {
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    clientName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    clientCompany: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
  });

  useEffect(() => {
    loadClients();
    if (estimateId) {
      loadEstimate();
    }
    // Set default valid until date (30 days from now)
    const validUntil = new Date();
    validUntil.setDate(validUntil.getDate() + 30);
    setFormData(prev => ({
      ...prev,
      validUntil: validUntil.toISOString().split('T')[0],
    }));
  }, [estimateId]);

  const loadClients = async () => {
    try {
      const clientsData = await clientService.getAll();
      setClients(clientsData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load clients');
    }
  };

  const loadEstimate = async () => {
    try {
      const estimate = await estimateService.getById(estimateId!);
      if (estimate) {
        setFormData({
          clientId: estimate.clientId,
          title: estimate.title,
          description: estimate.description,
          validUntil: estimate.validUntil.split('T')[0],
          items: estimate.items,
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load estimate');
    }
  };

  const calculateTotal = () => {
    const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.08; // 8% tax
    const total = subtotal + tax;
    return { subtotal, tax, total };
  };

  const handleSave = async () => {
    if (!formData.clientId) {
      Alert.alert('Error', 'Please select a client');
      return;
    }
    if (!formData.title.trim()) {
      Alert.alert('Error', 'Please enter a title');
      return;
    }
    if (formData.items.length === 0) {
      Alert.alert('Error', 'Please add at least one item');
      return;
    }

    setIsLoading(true);
    try {
      if (estimateId) {
        await estimateService.update(estimateId, formData);
        Alert.alert('Success', 'Estimate updated successfully');
      } else {
        await estimateService.create(formData);
        Alert.alert('Success', 'Estimate created successfully');
      }
      navigation.goBack();
    } catch (error) {
      Alert.alert('Error', 'Failed to save estimate');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddItem = () => {
    if (!newItem.description?.trim()) {
      Alert.alert('Error', 'Please enter item description');
      return;
    }
    if (!newItem.unitPrice || newItem.unitPrice <= 0) {
      Alert.alert('Error', 'Please enter a valid unit price');
      return;
    }

    const item: EstimateItem = {
      id: Date.now().toString(),
      description: newItem.description!,
      quantity: newItem.quantity || 1,
      unit: newItem.unit || 'each',
      unitPrice: newItem.unitPrice!,
      total: (newItem.quantity || 1) * newItem.unitPrice!,
      category: newItem.category || 'General',
    };

    setFormData(prev => ({
      ...prev,
      items: [...prev.items, item],
    }));

    setNewItem({
      description: '',
      quantity: 1,
      unit: 'each',
      unitPrice: 0,
      category: '',
    });
    setShowItemModal(false);
  };

  const handleRemoveItem = (itemId: string) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId),
    }));
  };

  const selectedClient = clients.find(client => client.id === formData.clientId);
  const { subtotal, tax, total } = calculateTotal();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {estimateId ? 'Edit Estimate' : 'New Estimate'}
        </Text>
        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Ionicons name="checkmark" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Client</Text>
          <TouchableOpacity
            style={styles.clientSelector}
            onPress={() => setShowClientModal(true)}
          >
            <Text style={[styles.clientText, !selectedClient && styles.clientPlaceholder]}>
              {selectedClient
                ? `${selectedClient.firstName} ${selectedClient.lastName}${selectedClient.company ? ` (${selectedClient.company})` : ''}`
                : 'Select a client'
              }
            </Text>
            <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Estimate Details</Text>
          <ThemedTextInput
            label="Title"
            placeholder="Enter estimate title"
            value={formData.title}
            onChangeText={(value) => setFormData(prev => ({ ...prev, title: value }))}
          />
          <ThemedTextInput
            label="Description"
            placeholder="Enter estimate description"
            value={formData.description}
            onChangeText={(value) => setFormData(prev => ({ ...prev, description: value }))}
            multiline
            numberOfLines={3}
          />
          <ThemedTextInput
            label="Valid Until"
            placeholder="YYYY-MM-DD"
            value={formData.validUntil}
            onChangeText={(value) => setFormData(prev => ({ ...prev, validUntil: value }))}
          />
        </View>

        <View style={styles.section}>
          <View style={styles.itemsHeader}>
            <Text style={styles.sectionTitle}>Items</Text>
            <TouchableOpacity
              style={styles.addItemButton}
              onPress={() => setShowItemModal(true)}
            >
              <Ionicons name="add-circle" size={28} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>

          {formData.items.map((item) => (
            <ThemedCard key={item.id} style={styles.itemCard}>
              <View style={styles.itemRow}>
                <View style={styles.itemInfo}>
                  <Text style={styles.itemDescription}>{item.description}</Text>
                  <Text style={styles.itemDetails}>
                    {item.quantity} {item.unit} × ${item.unitPrice.toFixed(2)}
                  </Text>
                </View>
                <Text style={styles.itemPrice}>${item.total.toFixed(2)}</Text>
                <View style={styles.itemActions}>
                  <TouchableOpacity
                    style={styles.itemActionButton}
                    onPress={() => handleRemoveItem(item.id)}
                  >
                    <Ionicons name="trash" size={20} color={theme.colors.error} />
                  </TouchableOpacity>
                </View>
              </View>
            </ThemedCard>
          ))}
        </View>

        {formData.items.length > 0 && (
          <ThemedCard style={styles.summary}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Subtotal</Text>
              <Text style={styles.summaryValue}>${subtotal.toFixed(2)}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Tax (8%)</Text>
              <Text style={styles.summaryValue}>${tax.toFixed(2)}</Text>
            </View>
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={[styles.summaryLabel, { fontWeight: 'bold' }]}>Total</Text>
              <Text style={styles.totalValue}>${total.toFixed(2)}</Text>
            </View>
          </ThemedCard>
        )}
      </ScrollView>

      {/* Client Selection Modal */}
      <ThemedModal
        visible={showClientModal}
        onClose={() => setShowClientModal(false)}
        title="Select Client"
        size="medium"
      >
        <ScrollView>
          {clients.map((client) => (
            <TouchableOpacity
              key={client.id}
              style={styles.clientItem}
              onPress={() => {
                setFormData(prev => ({ ...prev, clientId: client.id }));
                setShowClientModal(false);
              }}
            >
              <Text style={styles.clientName}>
                {client.firstName} {client.lastName}
              </Text>
              {client.company && (
                <Text style={styles.clientCompany}>{client.company}</Text>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </ThemedModal>

      {/* Add Item Modal */}
      <ThemedModal
        visible={showItemModal}
        onClose={() => setShowItemModal(false)}
        title="Add Item"
        size="large"
      >
        <View style={styles.modalContent}>
          <ThemedTextInput
            label="Description"
            placeholder="Enter item description"
            value={newItem.description || ''}
            onChangeText={(value) => setNewItem(prev => ({ ...prev, description: value }))}
          />
          <ThemedTextInput
            label="Quantity"
            placeholder="Enter quantity"
            value={newItem.quantity?.toString() || '1'}
            onChangeText={(value) => setNewItem(prev => ({ ...prev, quantity: parseInt(value) || 1 }))}
            keyboardType="numeric"
          />
          <ThemedTextInput
            label="Unit"
            placeholder="e.g., each, sq ft, linear ft"
            value={newItem.unit || ''}
            onChangeText={(value) => setNewItem(prev => ({ ...prev, unit: value }))}
          />
          <ThemedTextInput
            label="Unit Price"
            placeholder="Enter unit price"
            value={newItem.unitPrice?.toString() || ''}
            onChangeText={(value) => setNewItem(prev => ({ ...prev, unitPrice: parseFloat(value) || 0 }))}
            keyboardType="numeric"
          />
          <ThemedTextInput
            label="Category"
            placeholder="Enter category"
            value={newItem.category || ''}
            onChangeText={(value) => setNewItem(prev => ({ ...prev, category: value }))}
          />
          <View style={styles.modalButtons}>
            <ThemedButton
              title="Cancel"
              onPress={() => setShowItemModal(false)}
              variant="outline"
              style={styles.modalButton}
            />
            <ThemedButton
              title="Add Item"
              onPress={handleAddItem}
              variant="primary"
              style={styles.modalButton}
            />
          </View>
        </View>
      </ThemedModal>
    </SafeAreaView>
  );
};
