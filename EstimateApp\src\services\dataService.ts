import { Client, Estimate, Template, ClientForm, EstimateForm, EstimateStatus } from '../types';
import { mockData } from './supabase';

// Simulate API delays
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Client Services
export const clientService = {
  async getAll(): Promise<Client[]> {
    await delay(500);
    return mockData.clients;
  },

  async getById(id: string): Promise<Client | null> {
    await delay(300);
    return mockData.clients.find(client => client.id === id) || null;
  },

  async create(clientData: ClientForm): Promise<Client> {
    await delay(800);
    const newClient: Client = {
      id: Date.now().toString(),
      ...clientData,
      company: clientData.company || undefined,
      status: 'Active' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    mockData.clients.push(newClient);
    return newClient;
  },

  async update(id: string, clientData: Partial<ClientForm>): Promise<Client | null> {
    await delay(800);
    const clientIndex = mockData.clients.findIndex(client => client.id === id);
    if (clientIndex === -1) return null;

    mockData.clients[clientIndex] = {
      ...mockData.clients[clientIndex],
      ...clientData,
      updatedAt: new Date().toISOString(),
    };
    return mockData.clients[clientIndex];
  },

  async delete(id: string): Promise<boolean> {
    await delay(500);
    const clientIndex = mockData.clients.findIndex(client => client.id === id);
    if (clientIndex === -1) return false;

    mockData.clients.splice(clientIndex, 1);
    return true;
  },

  async search(query: string): Promise<Client[]> {
    await delay(300);
    const lowercaseQuery = query.toLowerCase();
    return mockData.clients.filter(client =>
      client.firstName.toLowerCase().includes(lowercaseQuery) ||
      client.lastName.toLowerCase().includes(lowercaseQuery) ||
      client.email.toLowerCase().includes(lowercaseQuery) ||
      client.company?.toLowerCase().includes(lowercaseQuery)
    );
  },
};

// Estimate Services
export const estimateService = {
  async getAll(): Promise<Estimate[]> {
    await delay(500);
    return mockData.estimates.map(estimate => ({
      ...estimate,
      client: mockData.clients.find(client => client.id === estimate.clientId),
    }));
  },

  async getById(id: string): Promise<Estimate | null> {
    await delay(300);
    const estimate = mockData.estimates.find(est => est.id === id);
    if (!estimate) return null;

    return {
      ...estimate,
      client: mockData.clients.find(client => client.id === estimate.clientId),
    };
  },

  async create(estimateData: EstimateForm): Promise<Estimate> {
    await delay(1000);
    const newEstimate: Estimate = {
      id: Date.now().toString(),
      ...estimateData,
      status: 'Draft' as const,
      subtotal: estimateData.items.reduce((sum, item) => sum + item.total, 0),
      tax: 0,
      total: 0,
      createdBy: '1', // Current user ID
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Calculate tax and total
    newEstimate.tax = newEstimate.subtotal * 0.08; // 8% tax
    newEstimate.total = newEstimate.subtotal + newEstimate.tax;

    mockData.estimates.push(newEstimate);
    return {
      ...newEstimate,
      client: mockData.clients.find(client => client.id === newEstimate.clientId),
    };
  },

  async update(id: string, estimateData: Partial<EstimateForm>): Promise<Estimate | null> {
    await delay(1000);
    const estimateIndex = mockData.estimates.findIndex(est => est.id === id);
    if (estimateIndex === -1) return null;

    const updatedEstimate = {
      ...mockData.estimates[estimateIndex],
      ...estimateData,
      updatedAt: new Date().toISOString(),
    };

    // Recalculate totals if items changed
    if (estimateData.items) {
      updatedEstimate.subtotal = estimateData.items.reduce((sum, item) => sum + item.total, 0);
      updatedEstimate.tax = updatedEstimate.subtotal * 0.08;
      updatedEstimate.total = updatedEstimate.subtotal + updatedEstimate.tax;
    }

    mockData.estimates[estimateIndex] = updatedEstimate;
    return {
      ...updatedEstimate,
      client: mockData.clients.find(client => client.id === updatedEstimate.clientId),
    };
  },

  async delete(id: string): Promise<boolean> {
    await delay(500);
    const estimateIndex = mockData.estimates.findIndex(est => est.id === id);
    if (estimateIndex === -1) return false;

    mockData.estimates.splice(estimateIndex, 1);
    return true;
  },

  async updateStatus(id: string, status: EstimateStatus): Promise<Estimate | null> {
    await delay(500);
    const estimateIndex = mockData.estimates.findIndex(est => est.id === id);
    if (estimateIndex === -1) return null;

    mockData.estimates[estimateIndex].status = status;
    mockData.estimates[estimateIndex].updatedAt = new Date().toISOString();

    return {
      ...mockData.estimates[estimateIndex],
      client: mockData.clients.find(client => client.id === mockData.estimates[estimateIndex].clientId),
    };
  },
};

// Template Services
export const templateService = {
  async getAll(): Promise<Template[]> {
    await delay(400);
    return mockData.templates;
  },

  async getById(id: string): Promise<Template | null> {
    await delay(200);
    return mockData.templates.find(template => template.id === id) || null;
  },

  async getByCategory(category: Template['category']): Promise<Template[]> {
    await delay(300);
    return mockData.templates.filter(template => template.category === category);
  },

  async search(query: string): Promise<Template[]> {
    await delay(300);
    const lowercaseQuery = query.toLowerCase();
    return mockData.templates.filter(template =>
      template.name.toLowerCase().includes(lowercaseQuery) ||
      template.description.toLowerCase().includes(lowercaseQuery) ||
      template.category.toLowerCase().includes(lowercaseQuery)
    );
  },
};

// Dashboard Services
export const dashboardService = {
  async getStats() {
    await delay(600);
    const totalEstimates = mockData.estimates.length;
    const pendingApprovals = mockData.estimates.filter(est => est.status === 'Pending').length;
    const activeClients = mockData.clients.filter(client => client.status === 'Active').length;
    const totalRevenue = mockData.estimates
      .filter(est => est.status === 'Approved')
      .reduce((sum, est) => sum + est.total, 0);

    return {
      totalEstimates,
      pendingApprovals,
      activeClients,
      totalRevenue,
      recentActivity: [
        {
          id: '1',
          type: 'estimate_created' as const,
          description: 'New estimate created for Kitchen Renovation',
          timestamp: '2 hours ago',
          userId: '1',
          userName: 'John Admin',
        },
        {
          id: '2',
          type: 'client_added' as const,
          description: 'New client Emily Brown added',
          timestamp: '4 hours ago',
          userId: '1',
          userName: 'John Admin',
        },
        {
          id: '3',
          type: 'estimate_approved' as const,
          description: 'Bathroom Renovation estimate approved',
          timestamp: '1 day ago',
          userId: '2',
          userName: 'Jane Manager',
        },
      ],
    };
  },
};
