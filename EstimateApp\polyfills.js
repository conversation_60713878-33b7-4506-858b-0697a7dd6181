// React Native polyfills for Node.js modules
import 'react-native-url-polyfill/auto';

// Buffer polyfill
import { <PERSON><PERSON><PERSON> } from 'buffer';
global.Buffer = Buffer;

// Process polyfill
import process from 'process';
global.process = process;

// Events polyfill
import { EventEmitter } from 'events';
global.EventEmitter = EventEmitter;

// Stream polyfill
import { Readable, Writable, Transform, PassThrough } from 'stream-browserify';
global.Stream = {
  Readable,
  Writable,
  Transform,
  PassThrough,
};

// Util polyfill
import util from 'util';
global.util = util;

// Additional polyfills for Supabase compatibility
if (typeof global.self === 'undefined') {
  global.self = global;
}

if (typeof global.window === 'undefined') {
  global.window = global;
}

// WebSocket is natively supported in React Native
// Block ws module usage by providing empty implementation
global.WebSocket = global.WebSocket || class MockWebSocket {
  constructor() {
    console.warn('Using native WebSocket implementation');
  }
};

// TextEncoder/TextDecoder polyfills
if (typeof global.TextEncoder === 'undefined') {
  const { TextEncoder, TextDecoder } = require('text-encoding');
  global.TextEncoder = TextEncoder;
  global.TextDecoder = TextDecoder;
}

// Crypto polyfill for React Native
import crypto from 'react-native-crypto';
global.crypto = crypto;

// Additional Node.js globals that might be needed
global.setImmediate = global.setImmediate || ((fn, ...args) => setTimeout(fn, 0, ...args));
global.clearImmediate = global.clearImmediate || clearTimeout;
