import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTheme } from '../contexts/ThemeContext';

// Import screens
import { ClientsListScreen } from '../screens/ClientsListScreen';
import { ClientDetailScreen } from '../screens/ClientDetailScreen';
import { AddEditClientScreen } from '../screens/AddEditClientScreen';

export type ClientsStackParamList = {
  ClientsList: undefined;
  ClientDetail: { clientId: string };
  AddEditClient: { clientId?: string };
};

const Stack = createStackNavigator<ClientsStackParamList>();

export const ClientsNavigator: React.FC = () => {
  const { theme } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.surface,
          borderBottomColor: theme.colors.border,
        },
        headerTintColor: theme.colors.text,
        headerTitleStyle: {
          fontWeight: '600',
        },
      }}
    >
      <Stack.Screen 
        name="ClientsList" 
        component={ClientsListScreen}
        options={{ title: 'Clients' }}
      />
      <Stack.Screen 
        name="ClientDetail" 
        component={ClientDetailScreen}
        options={{ title: 'Client Details' }}
      />
      <Stack.Screen 
        name="AddEditClient" 
        component={AddEditClientScreen}
        options={({ route }) => ({
          title: route.params?.clientId ? 'Edit Client' : 'Add Client',
        })}
      />
    </Stack.Navigator>
  );
};
