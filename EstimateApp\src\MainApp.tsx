import React, { useState } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { useTheme } from './contexts/ThemeContext';
import { useAuth } from './contexts/AuthContext';
import { WelcomeScreen, LoginScreen, RegisterScreen } from './screens';
import { TabNavigator } from './navigation/TabNavigator';

type AppScreen = 'welcome' | 'login' | 'register' | 'main';

export const MainApp: React.FC = () => {
  const { theme } = useTheme();
  const { authState } = useAuth();
  const [currentScreen, setCurrentScreen] = useState<AppScreen>('welcome');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
    },
  });

  // Show loading screen while checking authentication
  if (authState.isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  // If user is authenticated, show main app
  if (authState.isAuthenticated && authState.user) {
    return <TabNavigator />;
  }

  // Show authentication flow
  const renderScreen = () => {
    switch (currentScreen) {
      case 'login':
        return (
          <LoginScreen
            onBack={() => setCurrentScreen('welcome')}
            onRegister={() => setCurrentScreen('register')}
          />
        );
      case 'register':
        return (
          <RegisterScreen
            onBack={() => setCurrentScreen('welcome')}
            onLogin={() => setCurrentScreen('login')}
          />
        );
      default:
        return (
          <WelcomeScreen
            onLogin={() => setCurrentScreen('login')}
            onRegister={() => setCurrentScreen('register')}
          />
        );
    }
  };

  return <View style={styles.container}>{renderScreen()}</View>;
};

