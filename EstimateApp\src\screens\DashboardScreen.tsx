import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, ScrollView, TouchableOpacity, RefreshControl } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { ThemedButton } from '../components/ThemedButton';
import { ThemedCard } from '../components/ThemedCard';
import { dashboardService } from '../services/dataService';

interface ActivityItem {
  type: string;
  description: string;
  userName: string;
  timestamp: string;
}

export const DashboardScreen: React.FC = () => {
  const { theme, toggleTheme } = useTheme();
  const { authState, logout } = useAuth();
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const dashboardStats = await dashboardService.getStats();
      setStats(dashboardStats);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadDashboardData();
    setIsRefreshing(false);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 24,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    logo: {
      width: 40,
      height: 40,
      backgroundColor: theme.colors.primary,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
    },
    headerRight: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    iconButton: {
      padding: 8,
    },
    content: {
      flex: 1,
      paddingHorizontal: 24,
      paddingTop: 24,
    },
    welcomeContainer: {
      marginBottom: 32,
    },
    welcomeText: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
    },
    welcomeSubtext: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    statsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 16,
      marginBottom: 32,
    },
    statCard: {
      flex: 1,
      minWidth: 150,
      backgroundColor: theme.colors.surface,
      padding: 20,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    statValue: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.primary,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    quickActionsContainer: {
      marginBottom: 32,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 16,
    },
    quickActions: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    actionCard: {
      flex: 1,
      minWidth: 140,
      backgroundColor: theme.colors.surface,
      padding: 16,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      alignItems: 'center',
    },
    actionIcon: {
      width: 48,
      height: 48,
      backgroundColor: theme.colors.primary,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 12,
    },
    actionTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      textAlign: 'center',
    },
    recentContainer: {
      marginBottom: 32,
    },
    recentItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      padding: 16,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginBottom: 8,
    },
    recentIcon: {
      width: 40,
      height: 40,
      backgroundColor: theme.colors.primary,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    recentContent: {
      flex: 1,
    },
    recentTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2,
    },
    recentSubtitle: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    recentTime: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
  });

  const statsData = stats ? [
    { label: 'Total Estimates', value: stats.totalEstimates.toString(), icon: 'document-text' },
    { label: 'Pending Approvals', value: stats.pendingApprovals.toString(), icon: 'time' },
    { label: 'Active Clients', value: stats.activeClients.toString(), icon: 'people' },
    { label: 'Total Revenue', value: `$${(stats.totalRevenue / 1000).toFixed(1)}K`, icon: 'trending-up' },
  ] : [];

  const quickActions = [
    { title: 'New Estimate', icon: 'add-circle', color: theme.colors.primary },
    { title: 'Add Client', icon: 'person-add', color: theme.colors.secondary },
    { title: 'View Templates', icon: 'library', color: theme.colors.success },
    { title: 'Reports', icon: 'analytics', color: theme.colors.warning },
  ];

  const recentActivity = stats?.recentActivity || [];

  const getActivityIcon = (type: string): keyof typeof Ionicons.glyphMap => {
    switch (type) {
      case 'estimate_created':
        return 'document-text';
      case 'estimate_approved':
        return 'checkmark-circle';
      case 'client_added':
        return 'person-add';
      case 'template_used':
        return 'library';
      default:
        return 'information-circle';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={styles.logo}>
            <Ionicons name="construct" size={24} color="#FFFFFF" />
          </View>
          <Text style={styles.headerTitle}>Construction Manager</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.iconButton} onPress={toggleTheme}>
            <Ionicons
              name={theme.isDark ? "sunny" : "moon"}
              size={24}
              color={theme.colors.text}
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={logout}>
            <Ionicons name="log-out-outline" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary}
          />
        }
      >
        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeText}>
            Welcome back, {authState.user?.firstName}!
          </Text>
          <Text style={styles.welcomeSubtext}>
            Here's what's happening with your projects today.
          </Text>
        </View>

        <View style={styles.statsContainer}>
          {statsData.map((stat, index) => (
            <ThemedCard key={index} style={styles.statCard}>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </ThemedCard>
          ))}
        </View>

        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            {quickActions.map((action, index) => (
              <TouchableOpacity key={index} style={styles.actionCard}>
                <View style={styles.actionIcon}>
                  <Ionicons
                    name={action.icon as keyof typeof Ionicons.glyphMap}
                    size={24}
                    color="#FFFFFF"
                  />
                </View>
                <Text style={styles.actionTitle}>{action.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.recentContainer}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          {recentActivity.map((item: ActivityItem, index: number) => (
            <ThemedCard key={index} style={styles.recentItem}>
              <View style={styles.recentIcon}>
                <Ionicons
                  name={getActivityIcon(item.type)}
                  size={20}
                  color="#FFFFFF"
                />
              </View>
              <View style={styles.recentContent}>
                <Text style={styles.recentTitle}>{item.description}</Text>
                <Text style={styles.recentSubtitle}>by {item.userName}</Text>
              </View>
              <Text style={styles.recentTime}>{item.timestamp}</Text>
            </ThemedCard>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
