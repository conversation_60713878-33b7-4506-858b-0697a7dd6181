import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTheme } from '../contexts/ThemeContext';

// Import screens
import { EstimatesListScreen } from '../screens/EstimatesListScreen';
import { EstimateDetailScreen } from '../screens/EstimateDetailScreen';
import { EstimateBuilderScreen } from '../screens/EstimateBuilderScreen';

export type EstimatesStackParamList = {
  EstimatesList: undefined;
  EstimateDetail: { estimateId: string };
  EstimateBuilder: { estimateId?: string; templateId?: string };
};

const Stack = createStackNavigator<EstimatesStackParamList>();

export const EstimatesNavigator: React.FC = () => {
  const { theme } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.surface,
          borderBottomColor: theme.colors.border,
        },
        headerTintColor: theme.colors.text,
        headerTitleStyle: {
          fontWeight: '600',
        },
      }}
    >
      <Stack.Screen 
        name="EstimatesList" 
        component={EstimatesListScreen}
        options={{ title: 'Estimates' }}
      />
      <Stack.Screen 
        name="EstimateDetail" 
        component={EstimateDetailScreen}
        options={{ title: 'Estimate Details' }}
      />
      <Stack.Screen 
        name="EstimateBuilder" 
        component={EstimateBuilderScreen}
        options={({ route }) => ({
          title: route.params?.estimateId ? 'Edit Estimate' : 'New Estimate',
        })}
      />
    </Stack.Navigator>
  );
};
