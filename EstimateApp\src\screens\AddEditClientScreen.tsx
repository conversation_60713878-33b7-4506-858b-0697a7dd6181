import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp as NavigationRouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useTheme } from '../contexts/ThemeContext';
import { ClientForm, Address } from '../types';
import { clientService } from '../services/dataService';
import { ThemedTextInput } from '../components/ThemedTextInput';
import { ThemedButton } from '../components/ThemedButton';
import { ClientsStackParamList } from '../navigation/ClientsNavigator';

type NavigationProp = StackNavigationProp<ClientsStackParamList, 'AddEditClient'>;
type AddEditClientRouteProp = NavigationRouteProp<ClientsStackParamList, 'AddEditClient'>;

export const AddEditClientScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<AddEditClientRouteProp>();
  const { clientId } = route.params;

  const [formData, setFormData] = useState<ClientForm>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    company: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA',
    },
  });
  const [errors, setErrors] = useState<{
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    company?: string;
    address?: Partial<Address>;
  }>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingClient, setIsLoadingClient] = useState(!!clientId);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
      textAlign: 'center',
    },
    placeholder: {
      width: 40,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 20,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 16,
    },
    row: {
      flexDirection: 'row',
      gap: 12,
    },
    halfWidth: {
      flex: 1,
    },
    buttonContainer: {
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
  });

  useEffect(() => {
    if (clientId) {
      loadClient();
    }
  }, [clientId]);

  const loadClient = async () => {
    try {
      setIsLoadingClient(true);
      const client = await clientService.getById(clientId!);
      if (client) {
        setFormData({
          firstName: client.firstName,
          lastName: client.lastName,
          email: client.email,
          phone: client.phone,
          company: client.company || '',
          address: client.address,
        });
      } else {
        Alert.alert('Error', 'Client not found');
        navigation.goBack();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load client');
      navigation.goBack();
    } finally {
      setIsLoadingClient(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ClientForm & { address: Partial<Address> }> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (!formData.address.street.trim()) {
      if (!newErrors.address) newErrors.address = {};
      newErrors.address.street = 'Street address is required';
    }

    if (!formData.address.city.trim()) {
      if (!newErrors.address) newErrors.address = {};
      newErrors.address.city = 'City is required';
    }

    if (!formData.address.state.trim()) {
      if (!newErrors.address) newErrors.address = {};
      newErrors.address.state = 'State is required';
    }

    if (!formData.address.zipCode.trim()) {
      if (!newErrors.address) newErrors.address = {};
      newErrors.address.zipCode = 'ZIP code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0 && (!newErrors.address || Object.keys(newErrors.address).length === 0);
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      if (clientId) {
        await clientService.update(clientId, formData);
        Alert.alert('Success', 'Client updated successfully');
      } else {
        await clientService.create(formData);
        Alert.alert('Success', 'Client created successfully');
      }
      navigation.goBack();
    } catch (error) {
      Alert.alert('Error', 'Failed to save client');
    } finally {
      setIsLoading(false);
    }
  };

  const updateFormData = (field: keyof ClientForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const updateAddress = (field: keyof Address, value: string) => {
    setFormData(prev => ({
      ...prev,
      address: { ...prev.address, [field]: value }
    }));
    if (errors.address?.[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        if (newErrors.address) {
          const newAddress = { ...newErrors.address };
          delete newAddress[field as keyof Address];
          newErrors.address = newAddress;
        }
        return newErrors;
      });
    }
  };

  if (isLoadingClient) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={{ color: theme.colors.text }}>Loading client...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {clientId ? 'Edit Client' : 'Add Client'}
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Personal Information</Text>

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <ThemedTextInput
                label="First Name"
                placeholder="Enter first name"
                value={formData.firstName}
                onChangeText={(value) => updateFormData('firstName', value)}
                error={errors.firstName}
              />
            </View>
            <View style={styles.halfWidth}>
              <ThemedTextInput
                label="Last Name"
                placeholder="Enter last name"
                value={formData.lastName}
                onChangeText={(value) => updateFormData('lastName', value)}
                error={errors.lastName}
              />
            </View>
          </View>

          <ThemedTextInput
            label="Email"
            placeholder="Enter email address"
            value={formData.email}
            onChangeText={(value) => updateFormData('email', value)}
            keyboardType="email-address"
            autoCapitalize="none"
            leftIcon="mail-outline"
            error={errors.email}
          />

          <ThemedTextInput
            label="Phone"
            placeholder="Enter phone number"
            value={formData.phone}
            onChangeText={(value) => updateFormData('phone', value)}
            keyboardType="phone-pad"
            leftIcon="call-outline"
            error={errors.phone}
          />

          <ThemedTextInput
            label="Company (Optional)"
            placeholder="Enter company name"
            value={formData.company || ''}
            onChangeText={(value) => updateFormData('company', value)}
            leftIcon="business-outline"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Address</Text>

          <ThemedTextInput
            label="Street Address"
            placeholder="Enter street address"
            value={formData.address.street}
            onChangeText={(value) => updateAddress('street', value)}
            leftIcon="location-outline"
            error={errors.address?.street}
          />

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <ThemedTextInput
                label="City"
                placeholder="Enter city"
                value={formData.address.city}
                onChangeText={(value) => updateAddress('city', value)}
                error={errors.address?.city}
              />
            </View>
            <View style={styles.halfWidth}>
              <ThemedTextInput
                label="State"
                placeholder="Enter state"
                value={formData.address.state}
                onChangeText={(value) => updateAddress('state', value)}
                error={errors.address?.state}
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <ThemedTextInput
                label="ZIP Code"
                placeholder="Enter ZIP code"
                value={formData.address.zipCode}
                onChangeText={(value) => updateAddress('zipCode', value)}
                error={errors.address?.zipCode}
              />
            </View>
            <View style={styles.halfWidth}>
              <ThemedTextInput
                label="Country"
                placeholder="Enter country"
                value={formData.address.country}
                onChangeText={(value) => updateAddress('country', value)}
              />
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <ThemedButton
          title={clientId ? 'Update Client' : 'Create Client'}
          onPress={handleSave}
          loading={isLoading}
          size="large"
        />
      </View>
    </SafeAreaView>
  );
};
